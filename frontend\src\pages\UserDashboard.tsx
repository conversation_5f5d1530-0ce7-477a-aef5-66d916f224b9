import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { getMyEvents, completeEvent } from "../services/eventService";
import { createDocument, createMockDocument } from "../services/documentService";
import { Event } from "../types/event";
import { Document } from "../types/document";
import LoadingSpinner from "../components/LoadingSpinner";
import { useAuth } from "../context/AuthContext";
import HandleEventModal from "../components/HandleEventModal";
import { MobileContainer, MobileCard, MobilePageHeader } from '../components/common/MobileUtils';
import { useMobile } from '../hooks/useMobile';

const UserDashboard: React.FC = () => {
  const { user } = useAuth();
  const { isMobile } = useMobile();
  const [events, setEvents] = useState<Event[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [file, setFile] = useState<File | null>(null);
  const [newExpiryDate, setNewExpiryDate] = useState("");
  const [selectedEvent, setSelectedEvent] = useState<Event | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [expiryType, setExpiryType] = useState<"date" | "niet_van_toepassing">("date");
  const [documentNotApplicable, setDocumentNotApplicable] = useState(false);
  const [versionStatus, setVersionStatus] = useState<"active" | "inactive">("active");


  // All users can handle all event types - no document type restrictions
  const canHandleEvent = (eventType: string): boolean => {
    // All authenticated users can handle events assigned to them
    return true;
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        const eventsResponse = await getMyEvents();
        setEvents(eventsResponse?.events || []);
        setLoading(false);
      } catch (err: any) {
        const errorMessage = err.response?.data?.error || err.message || "Failed to fetch data. Please try again.";
        setError(errorMessage);
        setLoading(false);
      }
    };
    fetchData();
  }, []);

  const handleCompleteWithFile = async (event: Event) => {
    // All users can handle events assigned to them

    // Validation checks
    if (!documentNotApplicable && !file) {
      setError("Please select a file or mark document as 'Niet van toepassing'.");
      return;
    }

    if (expiryType === "date" && !newExpiryDate && !documentNotApplicable) {
      setError("Please set a new expiry date or mark validity as 'Niet van toepassing'.");
      return;
    }

    // Removed customer_id check to allow completing events without a customer

    setSubmitting(true);

    try {
      // Step 1: Create document if needed and if customer_id exists
      if (!documentNotApplicable && event.customer_id !== null) {
        const formattedExpiryDate = expiryType === "date" ? newExpiryDate : undefined;

        await createDocument(
          event.customer_id,
          event.id,
          file,
          event.event_type,
          expiryType,
          formattedExpiryDate,
          undefined,
          false,
          documentNotApplicable,
          versionStatus
        );
      }

      // Step 2: Complete the event
      await completeEvent(event.id);

      // Step 3: Reset state and refresh events
      setFile(null);
      setNewExpiryDate("");
      setExpiryType("date");
      setDocumentNotApplicable(false);
      setVersionStatus("active");
      setSelectedEvent(null);
      setError(null);

      // Refresh the events list
      try {
        const response = await getMyEvents();
        if (response && response.events) {
          console.log("Events refreshed after completion:", response.events.length);
          setEvents(response.events);
        } else {
          console.error("Invalid response from getMyEvents:", response);
        }
      } catch (refreshErr) {
        console.error("Error refreshing events:", refreshErr);
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.error || err.message || "Failed to complete event with file. Please try again.";
      setError(errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  const handleCompleteWithMock = async (event: Event) => {
    // All users can handle events assigned to them

    // Validation checks
    if (expiryType === "date" && !newExpiryDate) {
      setError("Please set a new expiry date or mark validity as 'Niet van toepassing'.");
      return;
    }

    // Removed customer_id check to allow completing events without a customer

    setSubmitting(true);

    try {
      // Step 1: Create mock document if customer_id exists
      if (event.customer_id !== null) {
        const formattedExpiryDate = expiryType === "date" ? newExpiryDate : undefined;

        await createMockDocument(
          event.customer_id,
          event.id,
          event.event_type,
          expiryType,
          formattedExpiryDate,
          undefined,
          false,
          documentNotApplicable,
          versionStatus
        );
      }

      // Step 2: Complete the event
      await completeEvent(event.id);

      // Step 3: Reset state and refresh events
      setFile(null);
      setNewExpiryDate("");
      setExpiryType("date");
      setDocumentNotApplicable(false);
      setVersionStatus("active");
      setSelectedEvent(null);
      setError(null);

      // Refresh the events list
      try {
        const response = await getMyEvents();
        if (response && response.events) {
          console.log("Events refreshed after mock completion:", response.events.length);
          setEvents(response.events);
        } else {
          console.error("Invalid response from getMyEvents:", response);
        }
      } catch (refreshErr) {
        console.error("Error refreshing events:", refreshErr);
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.error || err.message || "Failed to complete event with mock document. Please try again.";
      setError(errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  // The backend already filters events by user, so we don't need to filter by user_id here
  // Just filter by status since getMyEvents() already returns only events assigned to the current user
  const pendingEvents = events.filter((event) => event.status === "pending");
  const completedEvents = events.filter((event) => event.status === "completed");

  // Log for debugging
  console.log("Total events from backend:", events.length);
  console.log("Pending events:", pendingEvents.length);
  console.log("Completed events:", completedEvents.length);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "red":
        return "text-red-500";
      case "orange":
        return "text-orange-500";
      case "green":
        return "text-green-500";
      default:
        return "text-gray-500";
    }
  };

  const renderEventCards = (events: Event[], title: string) => {
    // Log the events being rendered for debugging
    console.log(`Rendering ${title}:`, events.length, events.map(e => ({id: e.id, user_id: e.user_id})));

    if (events.length === 0) {
      return (
        <div className="mb-8">
          <h2 className="text-2xl font-semibold text-amspm-text uppercase mb-4">{title}</h2>
          <p className="text-gray-500 italic">Geen gebeurtenissen gevonden</p>
        </div>
      );
    }

    return (
      <div className="mb-6 sm:mb-8">
        <h2 className="responsive-heading mb-4">{title}</h2>
        <div className="responsive-grid">
          {events.map((event) => (
            <MobileCard
              key={event.id}
              className={`${
                event.document?.expiry_status === "red"
                  ? "border-red-500"
                  : event.document?.expiry_status === "orange"
                  ? "border-orange-500"
                  : "border-green-500"
              }`}
            >
              <div className="mobile-card-content">
                <div className="mobile-card-row">
                  <span className="mobile-card-label">Klant:</span>
                  <span className="mobile-card-value">{event.customer_name || 'Geen klant'}</span>
                </div>
                <div className="mobile-card-row">
                  <span className="mobile-card-label">Adres:</span>
                  <span className="mobile-card-value">{event.customer_address || 'Geen adres'}</span>
                </div>
                <div className="mobile-card-row">
                  <span className="mobile-card-label">Type:</span>
                  <span className="mobile-card-value">{event.event_type}</span>
                </div>
                <div className="mobile-card-row">
                  <span className="mobile-card-label">Omschrijving:</span>
                  <span className="mobile-card-value">{event.description}</span>
                </div>
                <div className="mobile-card-row">
                  <span className="mobile-card-label">Gepland:</span>
                  <span className="mobile-card-value text-sm">
                    {new Date(event.scheduled_date).toLocaleString()}
                  </span>
                </div>
                <div className="mobile-card-row">
                  <span className="mobile-card-label">Status:</span>
                  <span className="mobile-card-value">
                    <span className={`status-dot ${event.status === "completed" ? "status-dot-green" : "status-dot-orange"}`}></span>
                    {event.status === "completed" ? "Voltooid" : "In behandeling"}
                  </span>
                </div>
                {event.completed_at && (
                  <div className="mobile-card-row">
                    <span className="mobile-card-label">Voltooid op:</span>
                    <span className="mobile-card-value text-sm">
                      {new Date(event.completed_at).toLocaleString()}
                    </span>
                  </div>
                )}
                {event.status === "completed" && event.completed_by_name && (
                  <div className="mobile-card-row">
                    <span className="mobile-card-label">Voltooid door:</span>
                    <span className="mobile-card-value">{event.completed_by_name}</span>
                  </div>
                )}
                {event.document && (
                  <div className="mobile-card-row">
                    <span className="mobile-card-label">Document Status:</span>
                    <span className={`mobile-card-value ${getStatusColor(event.document.expiry_status)}`}>
                      {event.document.expiry_status === "red" ? "Verlopen" :
                       event.document.expiry_status === "orange" ? "Verloopt binnenkort" : "Geldig"}
                    </span>
                  </div>
                )}
              </div>
              {event.status === "pending" && (
                <div className="mobile-card-actions">
                  <button
                    onClick={() => setSelectedEvent(event)}
                    className="btn btn-secondary mobile-touch-target w-full"
                    disabled={submitting}
                  >
                    Gebeurtenis Afhandelen
                  </button>
                </div>
              )}
            </MobileCard>
          ))}
        </div>
      </div>
    );
  };

  if (loading) {
    return <LoadingSpinner message="Dashboard laden..." />;
  }

  // Group events by customer for the "Go to Customer Documents" button
  const getCustomersWithPendingEvents = () => {
    // Since getMyEvents() already filters by current user, we don't need to filter again
    // Just filter out events without customers
    const pendingEventsWithCustomers = pendingEvents.filter(event => event.customer_id !== null);
    const customerMap = new Map<number, Event[]>();

    pendingEventsWithCustomers.forEach(event => {
      if (event.customer_id) {
        if (!customerMap.has(event.customer_id)) {
          customerMap.set(event.customer_id, []);
        }
        customerMap.get(event.customer_id)?.push(event);
      }
    });

    return customerMap;
  };

  // Render the customer documents section if user has pending events with customers
  const renderCustomerDocumentsSection = () => {
    const customersWithPendingEvents = getCustomersWithPendingEvents();

    if (customersWithPendingEvents.size === 0) {
      return null;
    }

    return (
      <div className="mb-8 bg-white rounded-lg shadow p-6">
        <h2 className="text-2xl font-semibold text-amspm-text mb-4">Klant Documenten</h2>
        <p className="text-gray-600 mb-4">U heeft openstaande gebeurtenissen voor de volgende klanten. Klik om hun documenten te bekijken:</p>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Array.from(customersWithPendingEvents.entries()).map(([customerId, customerEvents]) => (
            <Link
              key={customerId}
              to={`/customer/${customerId}/documents`}
              className="btn btn-primary flex items-center justify-center"
            >
              <span>{customerEvents[0].customer_name || `Customer ID: ${customerId}`}</span>
              <span className="ml-2 bg-white text-amspm-primary rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold">
                {customerEvents.length}
              </span>
            </Link>
          ))}
        </div>
      </div>
    );
  };

  return (
    <MobileContainer>
      <MobilePageHeader title="Gebruikers Dashboard" />
      {error && <p className="text-red-500 dark:text-red-400 mb-4">{error}</p>}
      {renderCustomerDocumentsSection()}
      {renderEventCards(pendingEvents, "Openstaande Gebeurtenissen")}
      {renderEventCards(completedEvents, "Voltooide Gebeurtenissen")}
      {selectedEvent && (
        <HandleEventModal
          event={selectedEvent}
          onClose={() => {
            setSelectedEvent(null);
            setFile(null);
            setNewExpiryDate("");
            setExpiryType("date");
            setDocumentNotApplicable(false);
            setVersionStatus("active");
          }}
          onCompleteWithFile={handleCompleteWithFile}
          onCompleteWithMock={handleCompleteWithMock}
          submitting={submitting}
          file={file}
          setFile={setFile}
          newExpiryDate={newExpiryDate}
          setNewExpiryDate={setNewExpiryDate}
          expiryType={expiryType}
          setExpiryType={setExpiryType}
          documentNotApplicable={documentNotApplicable}
          setDocumentNotApplicable={setDocumentNotApplicable}
          versionStatus={versionStatus}
          setVersionStatus={setVersionStatus}
        />
      )}
    </MobileContainer>
  );
};

export default UserDashboard;
