# app/repositories/document_repository.py
from app import db
from app.models.document import Document, ALLOWED_DOCUMENT_TYPES
from typing import List, Optional
from datetime import datetime, timedelta, timezone
from sqlalchemy import and_, or_, func, distinct
import logging

logger = logging.getLogger(__name__)

class DocumentRepository:
    def get_all_by_customer(self, customer_id: int) -> List[Document]:
        return Document.query.filter_by(customer_id=customer_id).all()

    def get_by_id(self, document_id: int) -> Optional[Document]:
        return Document.query.get(document_id)

    def get_all_by_related_document(self, related_document_id: int) -> List[Document]:
        """
        Fetch all documents that are sub-documents of a given document (via related_document_id).
        """
        return Document.query.filter_by(related_document_id=related_document_id).all()

    def get_upcoming_expirations(self, page: int = 1, per_page: int = 20) -> tuple[List[Document], int]:
        # Gebruik een UTC datetime voor consistentie
        now = datetime.now(timezone.utc)
        threshold_date = now + timedelta(days=30)

        # Subquery to find customers with a document_renewal
        renewed_customers = (
            Document.query
            .filter(
                and_(
                    Document.document_type == "document_renewal",
                    Document.status == "active"
                )
            )
            .with_entities(Document.customer_id)
            .distinct()
            .subquery()
        )

        # Import allowed document types from the model
        from app.models.document import ALLOWED_DOCUMENT_TYPES

        # Use all allowed document types for expiration checking
        main_document_types = ALLOWED_DOCUMENT_TYPES

        # Join with Customer table to get customer name
        from app.models.customer import Customer
        from sqlalchemy.orm import joinedload

        # Gebruik func.date() om de tijdzone-informatie te negeren bij vergelijkingen
        documents = Document.query.options(joinedload(Document.customer)).filter(
            and_(
                Document.expiry_date.isnot(None),
                func.date(Document.expiry_date) >= func.date(now),
                func.date(Document.expiry_date) <= func.date(threshold_date),
                Document.status == "active",  # Using status for active version check
                Document.document_type.in_(main_document_types),
                or_(
                    Document.document_type != "beveiligingscertificaat",
                    Document.customer_id.notin_(renewed_customers)
                )
            )
        ).paginate(page=page, per_page=per_page, error_out=False)

        return documents.items, documents.total

    def get_expired_documents(self, page: int = 1, per_page: int = 20) -> tuple[List[Document], int]:
        # Import allowed document types from the model
        from app.models.document import ALLOWED_DOCUMENT_TYPES

        # Use all allowed document types for expiration checking
        main_document_types = ALLOWED_DOCUMENT_TYPES

        # Gebruik een UTC datetime voor consistentie
        now = datetime.now(timezone.utc)

        # Join with Customer table to get customer name
        from app.models.customer import Customer
        from sqlalchemy.orm import joinedload

        # Gebruik func.date() om de tijdzone-informatie te negeren bij vergelijkingen
        documents = Document.query.options(joinedload(Document.customer)).filter(
            and_(
                Document.expiry_date.isnot(None),
                func.date(Document.expiry_date) < func.date(now),
                Document.status == "active",  # Using status for active version check
                Document.document_type.in_(main_document_types)
            )
        ).order_by(Document.expiry_date.asc()).paginate(page=page, per_page=per_page, error_out=False)

        return documents.items, documents.total

    def create(self, customer_id: int, event_id: Optional[int], file_url: str, file_path: str, document_type: str, uploaded_by: int, expiry_date: Optional[datetime] = None, related_document_id: Optional[int] = None, version_status: str = "active") -> Document:
        document = Document(
            customer_id=customer_id,
            event_id=event_id,
            file_url=file_url,
            file_path=file_path,
            document_type=document_type,
            uploaded_by=uploaded_by,
            expiry_date=expiry_date,
            status=version_status,
            related_document_id=related_document_id
        )
        db.session.add(document)
        db.session.commit()
        return document

    def delete(self, document_id: int) -> bool:
        document = self.get_by_id(document_id)
        if not document:
            # Log the error and return False to indicate failure
            logger.error(f"Document with ID {document_id} not found in repository delete method")
            return False
        try:
            db.session.delete(document)
            db.session.commit()
            return True
        except Exception as e:
            db.session.rollback()
            logger.error(f"Error deleting document {document_id}: {str(e)}")
            raise

    def get_most_recent_by_customer(self, customer_id: int) -> List[Document]:
        """
        Get all documents for a customer, including both active and inactive.
        """
        return (
            Document.query
            .filter(Document.customer_id == customer_id)
            .order_by(Document.created_at.desc())
            .all()
        )
